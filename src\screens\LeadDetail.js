import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, StatusBar, ScrollView, ActivityIndicator } from 'react-native';
import { <PERSON>er, Card, Button, Badge, colors, typography } from '../components/UIComponents';
import { API_ENDPOINTS } from '../config/api';

export default function LeadDetail({ route, navigation }) {
  const { leadId } = route.params;
  const [lead, setLead] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchLead = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_ENDPOINTS.LEADS}/${leadId}`);
      if (!response.ok) throw new Error('Failed to fetch lead');
      const data = await response.json();
      if (data.success) {
        setLead(data.data);
      } else {
        setError(data.message || 'Failed to fetch lead');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLead();
  }, [leadId]);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          title="Lead Details"
          leftComponent={
            <Button
              title="Back"
              variant="outline"
              size="small"
              onPress={() => navigation.goBack()}
            />
          }
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={{ marginTop: 16, color: colors.text.secondary }}>Loading lead...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !lead) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          title="Lead Not Found"
          leftComponent={
            <Button
              title="Back"
              variant="outline"
              size="small"
              onPress={() => navigation.goBack()}
            />
          }
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error || 'Lead not found'}</Text>
          <Text style={{ color: colors.primary, fontWeight: '600', marginTop: 16 }} onPress={fetchLead}>Tap to retry</Text>
        </View>
      </SafeAreaView>
    );
  }

  const handleAction = (action) => {
    switch (action) {
      case 'call':
        navigation.navigate('Call', { leadId });
        break;
      case 'chat':
        navigation.navigate('Chat', { leadId });
        break;
      case 'email':
        navigation.navigate('Email', { leadId });
        break;
      default:
        break;
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'new':
        return <Badge text="New Lead" variant="primary" />;
      case 'contacted':
        return <Badge text="Contacted" variant="warning" />;
      case 'qualified':
        return <Badge text="Qualified" variant="success" />;
      default:
        return <Badge text="Unknown" variant="secondary" />;
    }
  };

  const getPriorityBadge = (priority) => {
    switch (priority) {
      case 'high':
        return <Badge text="High" variant="danger" />;
      case 'medium':
        return <Badge text="Medium" variant="warning" />;
      case 'low':
        return <Badge text="Low" variant="secondary" />;
      default:
        return <Badge text="Unknown" variant="secondary" />;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      <Header
        title="Lead Details"
        leftComponent={
          <Button
            title="Back"
            variant="outline"
            size="small"
            onPress={() => navigation.goBack()}
          />
        }
        rightComponent={
          getStatusBadge(lead.status)
        }
      />

      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Lead Info Card */}
        <Card style={styles.leadInfoCard}>
          <View style={styles.leadHeader}>
            <View style={styles.leadAvatar}>
              <Text style={styles.leadAvatarText}>
                {lead.title.charAt(0).toUpperCase()}
              </Text>
            </View>
            <View style={styles.leadBasicInfo}>
              <Text style={styles.leadName}>{lead.title}</Text>
              <Text style={styles.leadStatus}>{lead.status.charAt(0).toUpperCase() + lead.status.slice(1)} Lead</Text>
            </View>
          </View>

          <View style={styles.leadDetails}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Status</Text>
              {getStatusBadge(lead.status)}
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Priority</Text>
              {getPriorityBadge(lead.priority)}
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Source</Text>
              <Text style={styles.detailValue}>{lead.source}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Captured</Text>
              <Text style={styles.detailValue}>{formatDate(lead.capturedAt)}</Text>
            </View>
          </View>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionsSection}>
          <Text style={styles.sectionTitle}>Contact Actions</Text>
          
          <View style={styles.actionButtons}>
            <Button
              title="📞 Call Lead"
              onPress={() => handleAction('call')}
              variant="primary"
              style={styles.actionButton}
            />
            <Button
              title="💬 WhatsApp"
              onPress={() => handleAction('chat')}
              variant="success"
              style={styles.actionButton}
            />
            <Button
              title="✉️ Send Email"
              onPress={() => handleAction('email')}
              variant="outline"
              style={styles.actionButton}
            />
          </View>
        </View>

        {/* Lead History */}
        <Card style={styles.historyCard}>
          <Text style={styles.sectionTitle}>Lead History</Text>
          
          <View style={styles.historyItem}>
            <View style={styles.historyIcon}>
              <Text style={styles.historyIconText}>📥</Text>
            </View>
            <View style={styles.historyContent}>
              <Text style={styles.historyTitle}>Lead Captured</Text>
              <Text style={styles.historyTime}>{formatDate(lead.capturedAt)}</Text>
            </View>
          </View>
          
          <View style={styles.historyItem}>
            <View style={styles.historyIcon}>
              <Text style={styles.historyIconText}>👁️</Text>
            </View>
            <View style={styles.historyContent}>
              <Text style={styles.historyTitle}>Viewed by Agent</Text>
              <Text style={styles.historyTime}>1 hour ago</Text>
            </View>
          </View>

          {lead.status === 'contacted' && (
            <View style={styles.historyItem}>
              <View style={styles.historyIcon}>
                <Text style={styles.historyIconText}>📞</Text>
              </View>
              <View style={styles.historyContent}>
                <Text style={styles.historyTitle}>Initial Contact Made</Text>
                <Text style={styles.historyTime}>2 hours ago</Text>
              </View>
            </View>
          )}

          {lead.status === 'qualified' && (
            <View style={styles.historyItem}>
              <View style={styles.historyIcon}>
                <Text style={styles.historyIconText}>✅</Text>
              </View>
              <View style={styles.historyContent}>
                <Text style={styles.historyTitle}>Lead Qualified</Text>
                <Text style={styles.historyTime}>1 day ago</Text>
      </View>
    </View>
          )}
        </Card>

        {/* Notes Section */}
        <Card style={styles.notesCard}>
          <Text style={styles.sectionTitle}>Notes</Text>
          <Text style={styles.notesText}>
            {lead.notes}
          </Text>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    ...typography.h3,
    color: colors.text.secondary,
  },
  leadInfoCard: {
    marginBottom: 20,
  },
  leadHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  leadAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  leadAvatarText: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text.white,
  },
  leadBasicInfo: {
    flex: 1,
  },
  leadName: {
    ...typography.h2,
    marginBottom: 4,
  },
  leadStatus: {
    ...typography.bodySmall,
    color: colors.text.secondary,
  },
  leadDetails: {
    gap: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    ...typography.bodySmall,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  detailValue: {
    ...typography.body,
    color: colors.text.primary,
    fontWeight: '500',
  },
  actionsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    ...typography.h3,
    marginBottom: 16,
  },
  actionButtons: {
    gap: 12,
  },
  actionButton: {
    marginBottom: 8,
  },
  historyCard: {
    marginBottom: 20,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  historyIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.background.tertiary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  historyIconText: {
    fontSize: 16,
  },
  historyContent: {
    flex: 1,
  },
  historyTitle: {
    ...typography.body,
    fontWeight: '500',
    marginBottom: 2,
  },
  historyTime: {
    ...typography.bodySmall,
    color: colors.text.secondary,
  },
  notesCard: {
    marginBottom: 20,
  },
  notesText: {
    ...typography.body,
    color: colors.text.secondary,
    lineHeight: 22,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});