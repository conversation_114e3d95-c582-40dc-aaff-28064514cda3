import React, { useState, useRef, useEffect } from 'react';
import { View, Text, FlatList, StyleSheet, SafeAreaView, StatusBar, KeyboardAvoidingView, Platform } from 'react-native';
import { Header, Input, Button, Card, colors, typography } from '../components/UIComponents';
import { leads } from '../utils/leads';

export default function ChatScreen({ route, navigation }) {
  const { leadId } = route.params;
  const lead = leads.find(l => l.id === leadId);
  const [messages, setMessages] = useState([
    {
      id: '1',
      text: 'Hi! I saw your interest in our services. How can I help you today?',
      sender: 'agent',
      timestamp: new Date(Date.now() - 300000), // 5 minutes ago
    }
  ]);
  const [text, setText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const flatListRef = useRef(null);

  useEffect(() => {
    // Simulate lead typing
    const timer = setTimeout(() => {
      setIsTyping(true);
      setTimeout(() => {
        setIsTyping(false);
        setMessages(prev => [...prev, {
          id: Date.now().toString(),
          text: 'Hi! Yes, I\'m interested in learning more about your premium package.',
          sender: 'lead',
          timestamp: new Date(),
        }]);
      }, 2000);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const sendMessage = () => {
    if (text.trim()) {
      const newMessage = {
        id: Date.now().toString(),
        text: text.trim(),
        sender: 'agent',
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, newMessage]);
      setText('');
      
      // Simulate lead response
      setTimeout(() => {
        setIsTyping(true);
        setTimeout(() => {
          setIsTyping(false);
          setMessages(prev => [...prev, {
            id: (Date.now() + 1).toString(),
            text: 'Thanks for the information! I\'ll review it and get back to you.',
            sender: 'lead',
            timestamp: new Date(),
          }]);
        }, 1500);
      }, 1000);
    }
  };

  const formatTime = (timestamp) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderMessage = ({ item }) => (
    <View style={[
      styles.messageContainer,
      item.sender === 'agent' ? styles.agentMessage : styles.leadMessage
    ]}>
      <View style={[
        styles.messageBubble,
        item.sender === 'agent' ? styles.agentBubble : styles.leadBubble
      ]}>
        <Text style={[
          styles.messageText,
          item.sender === 'agent' ? styles.agentText : styles.leadText
        ]}>
          {item.text}
        </Text>
        <Text style={[
          styles.messageTime,
          item.sender === 'agent' ? styles.agentTime : styles.leadTime
        ]}>
          {formatTime(item.timestamp)}
        </Text>
      </View>
    </View>
  );

  const renderTypingIndicator = () => {
    if (!isTyping) return null;
    
    return (
      <View style={styles.typingContainer}>
        <View style={styles.typingBubble}>
          <Text style={styles.typingText}>Typing...</Text>
        </View>
      </View>
    );
  };

  if (!lead) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          title="Lead Not Found"
          leftComponent={
            <Button
              title="Back"
              variant="outline"
              size="small"
              onPress={() => navigation.goBack()}
            />
          }
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Lead not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      <Header
        title={lead.title}
        subtitle="WhatsApp Chat"
        leftComponent={
          <Button
            title="Back"
            variant="outline"
            size="small"
            onPress={() => navigation.goBack()}
          />
        }
        rightComponent={
          <View style={styles.headerActions}>
            <Text style={styles.onlineStatus}>🟢 Online</Text>
          </View>
        }
      />

      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <View style={styles.content}>
          <FlatList
            ref={flatListRef}
            data={messages}
            keyExtractor={item => item.id}
            renderItem={renderMessage}
            contentContainerStyle={styles.messagesList}
            showsVerticalScrollIndicator={false}
            onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
            ListFooterComponent={renderTypingIndicator}
          />

          <View style={styles.inputContainer}>
            <View style={styles.inputWrapper}>
              <Input
                placeholder="Type a message..."
                value={text}
                onChangeText={setText}
                multiline
                numberOfLines={3}
                style={styles.messageInput}
              />
              <Button
                title="📤"
                onPress={sendMessage}
                disabled={!text.trim()}
                variant="primary"
                size="small"
                style={styles.sendButton}
              />
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    ...typography.h3,
    color: colors.text.secondary,
  },
  messagesList: {
    padding: 16,
  },
  messageContainer: {
    marginBottom: 12,
  },
  agentMessage: {
    alignItems: 'flex-end',
  },
  leadMessage: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
  },
  agentBubble: {
    backgroundColor: colors.primary,
    borderBottomRightRadius: 4,
  },
  leadBubble: {
    backgroundColor: colors.background.primary,
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    borderColor: colors.border,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 4,
  },
  agentText: {
    color: colors.text.white,
  },
  leadText: {
    color: colors.text.primary,
  },
  messageTime: {
    fontSize: 12,
    alignSelf: 'flex-end',
  },
  agentTime: {
    color: colors.text.white,
    opacity: 0.8,
  },
  leadTime: {
    color: colors.text.secondary,
  },
  typingContainer: {
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  typingBubble: {
    backgroundColor: colors.background.primary,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    borderColor: colors.border,
  },
  typingText: {
    ...typography.bodySmall,
    color: colors.text.secondary,
    fontStyle: 'italic',
  },
  inputContainer: {
    padding: 16,
    backgroundColor: colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
  },
  messageInput: {
    flex: 1,
    maxHeight: 100,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    paddingHorizontal: 0,
  },
  headerActions: {
    paddingHorizontal: 8,
  },
  onlineStatus: {
    ...typography.bodySmall,
    color: colors.accent,
    fontWeight: '600',
  },
});