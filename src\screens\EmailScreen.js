import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, StatusBar, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { Header, Input, Button, Card, colors, typography } from '../components/UIComponents';
import { leads } from '../utils/leads';

export default function EmailScreen({ route, navigation }) {
  const { leadId } = route.params;
  const lead = leads.find(l => l.id === leadId);
  const [subject, setSubject] = useState('');
  const [body, setBody] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSent, setIsSent] = useState(false);

  const emailTemplates = [
    {
      id: '1',
      title: 'Welcome Email',
      subject: 'Welcome to Our Services!',
      body: `Hi ${lead?.title || 'there'},

Thank you for your interest in our services. We're excited to have you on board!

Here's what you can expect from us:
• Personalized support
• Quality service delivery
• Regular updates

If you have any questions, feel free to reach out.

Best regards,
Your Team`
    },
    {
      id: '2',
      title: 'Follow-up Email',
      subject: 'Following up on our conversation',
      body: `Hi ${lead?.title || 'there'},

I hope this email finds you well. I wanted to follow up on our recent conversation about our services.

Is there anything specific you'd like to discuss or any questions you might have?

I'm here to help and would love to hear from you.

Best regards,
Your Name`
    },
    {
      id: '3',
      title: 'Custom Email',
      subject: '',
      body: ''
    }
  ];

  const handleTemplateSelect = (template) => {
    setSubject(template.subject);
    setBody(template.body);
  };

  const handleSendEmail = async () => {
    if (!subject.trim() || !body.trim()) {
      return;
    }
    
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      setIsSent(true);
    } catch (error) {
      console.error('Send email error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewEmail = () => {
    setSubject('');
    setBody('');
    setIsSent(false);
  };

  if (!lead) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          title="Lead Not Found"
          leftComponent={
            <Button
              title="Back"
              variant="outline"
              size="small"
              onPress={() => navigation.goBack()}
            />
          }
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Lead not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (isSent) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          title="Email Sent"
          leftComponent={
            <Button
              title="Back"
              variant="outline"
              size="small"
              onPress={() => navigation.goBack()}
            />
          }
        />
        <View style={styles.successContainer}>
          <View style={styles.successIcon}>
            <Text style={styles.successIconText}>✉️</Text>
          </View>
          <Text style={styles.successTitle}>Email Sent Successfully!</Text>
          <Text style={styles.successMessage}>
            Your email has been sent to {lead.email}
          </Text>
          <View style={styles.successActions}>
            <Button
              title="Send Another Email"
              onPress={handleNewEmail}
              variant="primary"
              style={styles.successButton}
            />
            <Button
              title="Back to Lead"
              onPress={() => navigation.goBack()}
              variant="outline"
              style={styles.successButton}
            />
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      <Header
        title="Compose Email"
        subtitle={`To: ${lead.email}`}
        leftComponent={
          <Button
            title="Back"
            variant="outline"
            size="small"
            onPress={() => navigation.goBack()}
          />
        }
      />

      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView 
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Email Templates */}
          <Card style={styles.templatesCard}>
            <Text style={styles.sectionTitle}>Email Templates</Text>
            <View style={styles.templatesList}>
              {emailTemplates.map((template) => (
                <Button
                  key={template.id}
                  title={template.title}
                  onPress={() => handleTemplateSelect(template)}
                  variant="secondary"
                  size="small"
                  style={styles.templateButton}
                />
              ))}
            </View>
          </Card>

          {/* Email Form */}
          <Card style={styles.emailForm}>
            <View style={styles.formGroup}>
              <Text style={styles.inputLabel}>To</Text>
              <View style={styles.recipientContainer}>
                <Text style={styles.recipientText}>{lead.email}</Text>
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.inputLabel}>Subject</Text>
              <Input
                placeholder="Enter email subject"
                value={subject}
                onChangeText={setSubject}
                style={styles.subjectInput}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.inputLabel}>Message</Text>
              <Input
                placeholder="Write your email message here..."
                value={body}
                onChangeText={setBody}
                multiline
                numberOfLines={8}
                style={styles.bodyInput}
              />
            </View>

            <View style={styles.formActions}>
              <Button
                title="📎 Attach File"
                variant="outline"
                size="medium"
                style={styles.actionButton}
              />
              <Button
                title="📧 Send Email"
                onPress={handleSendEmail}
                disabled={isLoading || !subject.trim() || !body.trim()}
                variant="primary"
                size="medium"
                style={styles.sendButton}
              />
            </View>
          </Card>

          {/* Email Preview */}
          {subject || body ? (
            <Card style={styles.previewCard}>
              <Text style={styles.sectionTitle}>Preview</Text>
              <View style={styles.previewContent}>
                <Text style={styles.previewLabel}>To:</Text>
                <Text style={styles.previewValue}>{lead.email}</Text>
                
                <Text style={styles.previewLabel}>Subject:</Text>
                <Text style={styles.previewValue}>{subject || 'No subject'}</Text>
                
                <Text style={styles.previewLabel}>Message:</Text>
                <Text style={styles.previewMessage}>{body || 'No message'}</Text>
              </View>
            </Card>
          ) : null}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    ...typography.h3,
    color: colors.text.secondary,
  },
  templatesCard: {
    marginBottom: 20,
  },
  sectionTitle: {
    ...typography.h3,
    marginBottom: 16,
  },
  templatesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  templateButton: {
    marginBottom: 8,
  },
  emailForm: {
    marginBottom: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    ...typography.bodySmall,
    fontWeight: '600',
    marginBottom: 8,
    color: colors.text.primary,
  },
  recipientContainer: {
    backgroundColor: colors.background.tertiary,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  recipientText: {
    ...typography.body,
    color: colors.text.primary,
  },
  subjectInput: {
    backgroundColor: colors.background.primary,
  },
  bodyInput: {
    backgroundColor: colors.background.primary,
    minHeight: 120,
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  sendButton: {
    flex: 2,
  },
  previewCard: {
    marginBottom: 20,
  },
  previewContent: {
    gap: 8,
  },
  previewLabel: {
    ...typography.bodySmall,
    fontWeight: '600',
    color: colors.text.secondary,
  },
  previewValue: {
    ...typography.body,
    color: colors.text.primary,
    marginBottom: 8,
  },
  previewMessage: {
    ...typography.body,
    color: colors.text.primary,
    lineHeight: 22,
  },
  // Success state styles
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  successIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.accent,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  successIconText: {
    fontSize: 40,
  },
  successTitle: {
    ...typography.h2,
    marginBottom: 16,
    textAlign: 'center',
  },
  successMessage: {
    ...typography.body,
    textAlign: 'center',
    color: colors.text.secondary,
    marginBottom: 32,
    lineHeight: 24,
  },
  successActions: {
    width: '100%',
    gap: 12,
  },
  successButton: {
    width: '100%',
  },
});