import { NativeModules, NativeEventEmitter, Platform } from 'react-native';

const { RNSinchModule } = NativeModules;

class SinchService {
  constructor() {
    this.isInitialized = false;
    this.isConnected = false;
    this.currentCall = null;
    this.eventEmitter = null;
    this.listeners = [];
  }

  // Initialize Sinch client
  async initialize(userId, sinchCredentials) {
    try {
      console.log('🔧 Initializing Sinch client for user:', userId);
      console.log('🔧 Credentials:', sinchCredentials);
      
      if (!RNSinchModule) {
        throw new Error('Sinch native module not found. Please ensure the native modules are properly linked.');
      }

      // Pass the credentials object to native module
      await RNSinchModule.initialize(userId, sinchCredentials);
      this.isInitialized = true;
      
      // Set up event listeners
      this.setupEventListeners();
      
      console.log('✅ Sinch client initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Sinch client:', error);
      throw error;
    }
  }

  // Set up event listeners for call events
  setupEventListeners() {
    if (!RNSinchModule) return;

    this.eventEmitter = new NativeEventEmitter(RNSinchModule);

    const events = [
      'onClientStarted',
      'onClientFailed',
      'onCallEstablished',
      'onCallEnded',
      'onCallFailed',
      'onCallProgressing',
      'onShouldSendPushNotification',
      'onIncomingCall',
      'onCallStateChanged'
    ];

    events.forEach(event => {
      const listener = this.eventEmitter.addListener(event, (data) => {
        console.log(`📞 Sinch Event [${event}]:`, data);
        this.handleCallEvent(event, data);
      });
      this.listeners.push(listener);
    });
  }

  // Handle call events
  handleCallEvent(event, data) {
    console.log(`📞 Sinch Event [${event}]:`, data);
    
    switch (event) {
      case 'onClientStarted':
        console.log('✅ Setting isConnected = true');
        this.isConnected = true;
        break;
      case 'onClientFailed':
        console.log('❌ Setting isConnected = false');
        this.isConnected = false;
        break;
      case 'onCallEstablished':
        console.log('📞 Call established, storing call data');
        this.currentCall = data;
        break;
      case 'onCallEnded':
        console.log('📞 Call ended, clearing call data');
        this.currentCall = null;
        break;
      case 'onCallFailed':
        console.log('❌ Call failed, clearing call data');
        this.currentCall = null;
        break;
      default:
        console.log(`📞 Unhandled event: ${event}`);
    }
  }

  // Make a call to a phone number
  async callPhoneNumber(phoneNumber) {
    try {
      if (!this.isInitialized) {
        throw new Error('Sinch client not initialized');
      }

      console.log('📞 Making call to:', phoneNumber);
      
      // Format phone number (ensure it starts with +)
      let formattedNumber = phoneNumber.trim();
      if (!formattedNumber.startsWith('+')) {
        formattedNumber = '+' + formattedNumber;
      }

      const callId = await RNSinchModule.callPhoneNumber(formattedNumber);
      console.log('✅ Call initiated with ID:', callId);
      
      return callId;
    } catch (error) {
      console.error('❌ Failed to make call:', error);
      throw error;
    }
  }

  // Answer incoming call
  async answerCall() {
    try {
      if (!this.isInitialized) {
        throw new Error('Sinch client not initialized');
      }

      console.log('📞 Answering incoming call');
      await RNSinchModule.answerCall();
      console.log('✅ Call answered');
    } catch (error) {
      console.error('❌ Failed to answer call:', error);
      throw error;
    }
  }

  // Hang up current call
  async hangup() {
    try {
      if (!this.isInitialized) {
        throw new Error('Sinch client not initialized');
      }

      console.log('📞 Hanging up call');
      await RNSinchModule.hangup();
      this.currentCall = null;
      console.log('✅ Call hung up');
    } catch (error) {
      console.error('❌ Failed to hang up call:', error);
      throw error;
    }
  }

  // Mute/unmute microphone
  async setMuted(muted) {
    try {
      if (!this.isInitialized) {
        throw new Error('Sinch client not initialized');
      }

      console.log('🎤 Setting muted:', muted);
      await RNSinchModule.setMuted(muted);
      console.log('✅ Mute state updated');
    } catch (error) {
      console.error('❌ Failed to set mute state:', error);
      throw error;
    }
  }

  // Enable/disable speaker
  async setSpeakerOn(speakerOn) {
    try {
      if (!this.isInitialized) {
        throw new Error('Sinch client not initialized');
      }

      console.log('🔊 Setting speaker:', speakerOn);
      await RNSinchModule.setSpeakerOn(speakerOn);
      console.log('✅ Speaker state updated');
    } catch (error) {
      console.error('❌ Failed to set speaker state:', error);
      throw error;
    }
  }

  // Get current call state
  getCallState() {
    return this.currentCall;
  }

  // Check if client is connected
  isClientConnected() {
    return this.isConnected;
  }

  // Check if client is initialized
  isClientInitialized() {
    return this.isInitialized;
  }

  // Clean up resources
  cleanup() {
    console.log('🧹 Cleaning up Sinch service');
    
    // Remove event listeners
    if (this.listeners.length > 0) {
      this.listeners.forEach(listener => listener.remove());
      this.listeners = [];
    }

    // Hang up any active call
    if (this.currentCall) {
      this.hangup().catch(console.error);
    }

    this.isInitialized = false;
    this.isConnected = false;
    this.currentCall = null;
  }

  // Check client state directly from native module
  async checkNativeClientState() {
    try {
      if (!RNSinchModule) {
        return { isReady: false, error: 'Native module not available' };
      }
      
      const state = await RNSinchModule.isClientReady();
      console.log('🔍 Native client state:', state);
      return state;
    } catch (error) {
      console.error('❌ Error checking native client state:', error);
      return { isReady: false, error: error.message };
    }
  }

  // Check if client is ready for calls
  async waitForClientReady(maxWaitTime = 10000) {
    const startTime = Date.now();
    
    console.log(`⏳ Waiting for client ready. Current state - isInitialized: ${this.isInitialized}, isConnected: ${this.isConnected}`);
    
    // First check native state directly
    const nativeState = await this.checkNativeClientState();
    if (nativeState.isReady) {
      console.log('✅ Native client is already ready for calls');
      this.isConnected = true; // Update our state to match
      return true;
    }
    
    // If already connected according to our state, return immediately
    if (this.isConnected) {
      console.log('✅ Sinch client is already ready for calls (according to JS state)');
      return true;
    }
    
    while (Date.now() - startTime < maxWaitTime) {
      // Check both JS state and native state
      const currentNativeState = await this.checkNativeClientState();
      console.log(`🔍 Checking states - JS isConnected: ${this.isConnected}, Native isReady: ${currentNativeState.isReady}`);
      
      if (currentNativeState.isReady || this.isConnected) {
        console.log('✅ Sinch client is ready for calls');
        this.isConnected = true; // Ensure JS state matches native state
        return true;
      }
      
      console.log('⏳ Waiting for Sinch client to be ready...');
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    const finalState = await this.checkNativeClientState();
    console.error(`❌ Timeout waiting for Sinch client. Final states - JS: isInitialized=${this.isInitialized}, isConnected=${this.isConnected}, Native: ${JSON.stringify(finalState)}`);
    throw new Error(`Timeout waiting for Sinch client to be ready. Native state: ${JSON.stringify(finalState)}`);
  }

  // Check if we can make calls right now
  isClientReady() {
    const ready = this.isInitialized && this.isConnected;
    console.log(`🔍 isClientReady check: isInitialized=${this.isInitialized}, isConnected=${this.isConnected}, ready=${ready}`);
    return ready;
  }
}

// Export singleton instance
export default new SinchService(); 