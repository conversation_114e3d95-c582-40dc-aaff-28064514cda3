#!/bin/bash

echo "🔧 Setting up Sinch for Android..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the React Native project root"
    exit 1
fi

echo "✅ Found React Native project"

# Check if Android directory exists
if [ ! -d "android" ]; then
    echo "❌ Error: Android directory not found"
    exit 1
fi

echo "✅ Android directory found"

# Check if the native modules already exist
if [ -f "android/app/src/main/java/com/leadgenapp/RNSinchModule.kt" ]; then
    echo "⚠️  RNSinchModule.kt already exists. Skipping creation."
else
    echo "📝 Creating RNSinchModule.kt..."
    # The file should already be created by the assistant
fi

if [ -f "android/app/src/main/java/com/leadgenapp/RNSinchPackage.kt" ]; then
    echo "⚠️  RNSinchPackage.kt already exists. Skipping creation."
else
    echo "📝 Creating RNSinchPackage.kt..."
    # The file should already be created by the assistant
fi

echo "🔧 Building Android project..."
cd android
./gradlew clean
./gradlew assembleDebug

if [ $? -eq 0 ]; then
    echo "✅ Android build successful!"
    echo ""
    echo "🎉 Sinch Android setup completed!"
    echo ""
    echo "Next steps:"
    echo "1. Run: npx react-native run-android"
    echo "2. Test calling functionality in your app"
    echo "3. Check logs for any Sinch-related errors"
else
    echo "❌ Android build failed. Please check the error messages above."
    exit 1
fi

cd .. 