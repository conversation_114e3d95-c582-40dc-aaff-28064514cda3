import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import LeadList from '../screens/LeadList';
import LeadDetail from '../screens/LeadDetail';
import CallScreen from '../screens/CallScreen';
import ChatScreen from '../screens/ChatScreen';
import EmailScreen from '../screens/EmailScreen';

const Stack = createNativeStackNavigator();

export default function MainStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Leads" component={LeadList} />
      <Stack.Screen name="LeadDetail" component={LeadDetail} />
      <Stack.Screen name="Call" component={CallScreen} />
      <Stack.Screen name="Chat" component={ChatScreen} />
      <Stack.Screen name="Email" component={EmailScreen} />
    </Stack.Navigator>
  );
}