import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import AgentSignIn from '../screens/AgentSignIn';
import ForgotPassword from '../screens/ForgotPassword';

const Stack = createNativeStackNavigator();

export default function AuthStack({ onSignIn }) {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="SignIn">
        {props => <AgentSignIn {...props} onSignIn={onSignIn} />}
      </Stack.Screen>
      <Stack.Screen name="ForgotPassword" component={ForgotPassword} />
    </Stack.Navigator>
  );
}