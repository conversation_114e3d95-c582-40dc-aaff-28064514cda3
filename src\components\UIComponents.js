import React from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet } from 'react-native';

// Color palette
export const colors = {
  primary: '#6366f1',
  primaryDark: '#4f46e5',
  secondary: '#f8fafc',
  accent: '#10b981',
  danger: '#ef4444',
  warning: '#f59e0b',
  text: {
    primary: '#1e293b',
    secondary: '#64748b',
    light: '#94a3b8',
    white: '#ffffff'
  },
  background: {
    primary: '#ffffff',
    secondary: '#f8fafc',
    tertiary: '#f1f5f9'
  },
  border: '#e2e8f0',
  shadow: '#000000'
};

// Typography
export const typography = {
  h1: { fontSize: 28, fontWeight: '700', color: colors.text.primary },
  h2: { fontSize: 24, fontWeight: '600', color: colors.text.primary },
  h3: { fontSize: 20, fontWeight: '600', color: colors.text.primary },
  body: { fontSize: 16, color: colors.text.primary },
  bodySmall: { fontSize: 14, color: colors.text.secondary },
  caption: { fontSize: 12, color: colors.text.light }
};

// Button Component
export const Button = ({ 
  title, 
  onPress, 
  variant = 'primary', 
  size = 'medium', 
  disabled = false,
  style,
  textStyle 
}) => {
  const buttonStyles = [
    styles.button,
    styles[`button${variant.charAt(0).toUpperCase() + variant.slice(1)}`],
    styles[`button${size.charAt(0).toUpperCase() + size.slice(1)}`],
    disabled && styles.buttonDisabled,
    style
  ];

  const textStyles = [
    styles.buttonText,
    styles[`buttonText${variant.charAt(0).toUpperCase() + variant.slice(1)}`],
    styles[`buttonText${size.charAt(0).toUpperCase() + size.slice(1)}`],
    disabled && styles.buttonTextDisabled,
    textStyle
  ];

  return (
    <TouchableOpacity 
      style={buttonStyles} 
      onPress={onPress} 
      disabled={disabled}
      activeOpacity={0.8}
    >
      <Text style={textStyles}>{title}</Text>
    </TouchableOpacity>
  );
};

// Input Component
export const Input = ({ 
  placeholder, 
  value, 
  onChangeText, 
  secureTextEntry = false,
  multiline = false,
  numberOfLines = 1,
  style,
  ...props 
}) => {
  return (
    <TextInput
      style={[styles.input, multiline && styles.inputMultiline, style]}
      placeholder={placeholder}
      placeholderTextColor={colors.text.light}
      value={value}
      onChangeText={onChangeText}
      secureTextEntry={secureTextEntry}
      multiline={multiline}
      numberOfLines={numberOfLines}
      {...props}
    />
  );
};

// Card Component
export const Card = ({ children, style, onPress }) => {
  const CardComponent = onPress ? TouchableOpacity : View;
  
  return (
    <CardComponent 
      style={[styles.card, style]} 
      onPress={onPress}
      activeOpacity={0.9}
    >
      {children}
    </CardComponent>
  );
};

// Header Component
export const Header = ({ title, subtitle, leftComponent, rightComponent }) => {
  return (
    <View style={styles.header}>
      {leftComponent && (
        <View style={styles.headerLeft}>
          {leftComponent}
        </View>
      )}
      <View style={styles.headerCenter}>
        <Text style={styles.headerTitle}>{title}</Text>
        {subtitle && <Text style={styles.headerSubtitle}>{subtitle}</Text>}
      </View>
      {rightComponent && (
        <View style={styles.headerRight}>
          {rightComponent}
        </View>
      )}
    </View>
  );
};

// Badge Component
export const Badge = ({ text, variant = 'primary', style }) => {
  return (
    <View style={[styles.badge, styles[`badge${variant.charAt(0).toUpperCase() + variant.slice(1)}`], style]}>
      <Text style={[styles.badgeText, styles[`badgeText${variant.charAt(0).toUpperCase() + variant.slice(1)}`]]}>
        {text}
      </Text>
    </View>
  );
};

// Divider Component
export const Divider = ({ style }) => {
  return <View style={[styles.divider, style]} />;
};

const styles = StyleSheet.create({
  // Button Styles
  button: {
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  buttonPrimary: {
    backgroundColor: colors.primary,
  },
  buttonSecondary: {
    backgroundColor: colors.background.secondary,
    borderWidth: 1,
    borderColor: colors.border,
  },
  buttonOutline: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: colors.primary,
  },
  buttonDanger: {
    backgroundColor: colors.danger,
  },
  buttonSuccess: {
    backgroundColor: colors.accent,
  },
  buttonSmall: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  buttonMedium: {
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  buttonLarge: {
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  buttonDisabled: {
    backgroundColor: colors.text.light,
    opacity: 0.6,
  },
  buttonText: {
    fontWeight: '600',
  },
  buttonTextPrimary: {
    color: colors.text.white,
  },
  buttonTextSecondary: {
    color: colors.text.primary,
  },
  buttonTextOutline: {
    color: colors.primary,
  },
  buttonTextDanger: {
    color: colors.text.white,
  },
  buttonTextSuccess: {
    color: colors.text.white,
  },
  buttonTextSmall: {
    fontSize: 14,
  },
  buttonTextMedium: {
    fontSize: 16,
  },
  buttonTextLarge: {
    fontSize: 18,
  },
  buttonTextDisabled: {
    color: colors.text.secondary,
  },

  // Input Styles
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.background.primary,
    fontSize: 16,
    color: colors.text.primary,
  },
  inputMultiline: {
    textAlignVertical: 'top',
    minHeight: 100,
  },

  // Card Styles
  card: {
    backgroundColor: colors.background.primary,
    borderRadius: 16,
    padding: 20,
    marginVertical: 8,
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },

  // Header Styles
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerLeft: {
    flex: 1,
    alignItems: 'flex-start',
  },
  headerCenter: {
    flex: 2,
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  headerTitle: {
    ...typography.h2,
    textAlign: 'center',
  },
  headerSubtitle: {
    ...typography.bodySmall,
    textAlign: 'center',
    marginTop: 2,
  },

  // Badge Styles
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgePrimary: {
    backgroundColor: colors.primary,
  },
  badgeSecondary: {
    backgroundColor: colors.background.tertiary,
  },
  badgeSuccess: {
    backgroundColor: colors.accent,
  },
  badgeWarning: {
    backgroundColor: colors.warning,
  },
  badgeDanger: {
    backgroundColor: colors.danger,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  badgeTextPrimary: {
    color: colors.text.white,
  },
  badgeTextSecondary: {
    color: colors.text.primary,
  },
  badgeTextSuccess: {
    color: colors.text.white,
  },
  badgeTextWarning: {
    color: colors.text.white,
  },
  badgeTextDanger: {
    color: colors.text.white,
  },

  // Divider Styles
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: 16,
  },
}); 