import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, KeyboardAvoidingView, Platform, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Button, Input, colors, typography } from '../components/UIComponents';
import { API_ENDPOINTS, testAPIConnection } from '../config/api';

export default function AgentSignIn({ navigation, onSignIn }) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isServerConnected, setIsServerConnected] = useState(false);

  // Test API connection on component mount
  useEffect(() => {
    testServerConnection();
  }, []);

  const testServerConnection = async () => {
    const connected = await testAPIConnection();
    setIsServerConnected(connected);
  };

  const handleSignIn = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }
    
    setIsLoading(true);
    try {
      console.log('Attempting sign in with:', { email: email.trim() });
      console.log('API endpoint:', API_ENDPOINTS.SIGN_IN);
      
      const response = await fetch(API_ENDPOINTS.SIGN_IN, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email: email.trim(), 
          password 
        }),
      });
      
      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Response data:', data);
      
      if (data.success) {
        // Store token and agent data (you might want to use AsyncStorage or a state management solution)
        console.log('Sign in successful:', data.data);
        
        // Call the onSignIn callback with the agent data and token
        if (onSignIn && typeof onSignIn === 'function') {
          onSignIn(data.data);
        }
        
        Alert.alert('Success', 'Sign in successful!');
      } else {
        Alert.alert('Error', data.message || 'Sign in failed');
      }
    } catch (error) {
      console.error('Sign in error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      
      if (error.message.includes('Network request failed')) {
        Alert.alert(
          'Connection Error', 
          'Unable to connect to server. Please check:\n\n1. Backend server is running\n2. IP address is correct in src/config/api.js\n3. Both devices are on same network'
        );
      } else if (error.message.includes('HTTP error')) {
        Alert.alert('Server Error', 'Server returned an error. Please try again.');
      } else {
        Alert.alert('Error', `Sign in failed: ${error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.content}>
            {/* Logo/Brand Section */}
            <View style={styles.brandSection}>
              <View style={styles.logoContainer}>
                <Text style={styles.logoText}>LC</Text>
              </View>
              <Text style={styles.brandTitle}>Lead Calling</Text>
              <Text style={styles.brandSubtitle}>Agent Portal</Text>
            </View>

            {/* Form Section */}
            <View style={styles.formSection}>
              <Text style={styles.formTitle}>Welcome Back</Text>
              <Text style={styles.formSubtitle}>Sign in to your account</Text>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Email Address</Text>
                <Input
                  placeholder="Enter your email"
        value={email}
        onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!isLoading}
      />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Password</Text>
                <Input
                  placeholder="Enter your password"
        value={password}
        onChangeText={setPassword}
                  secureTextEntry
                  autoCapitalize="none"
                  editable={!isLoading}
                />
              </View>

              <TouchableOpacity 
                style={styles.forgotPassword}
                onPress={() => navigation.navigate('ForgotPassword')}
                disabled={isLoading}
              >
                <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
      </TouchableOpacity>

              <Button
                title={isLoading ? "Signing In..." : "Sign In"}
                onPress={handleSignIn}
                disabled={isLoading || !email.trim() || !password.trim()}
                style={styles.signInButton}
              />
            </View>

            {/* Server Status */}
            <View style={[
              styles.serverInfo,
              { 
                backgroundColor: isServerConnected ? colors.accent + '20' : colors.warning + '20',
                borderLeftColor: isServerConnected ? colors.accent : colors.warning
              }
            ]}>
              <Text style={styles.serverInfoTitle}>
                Server Status: {isServerConnected ? '🟢 Connected' : '🔴 Disconnected'}
              </Text>
              <Text style={styles.serverInfoText}>
                Backend: {API_ENDPOINTS.SIGN_IN.replace('/auth/signin', '')}
              </Text>
              <Text style={styles.serverInfoText}>
                {isServerConnected 
                  ? 'Server is running and accessible' 
                  : 'Cannot connect to server. Check your IP address in src/config/api.js'
                }
              </Text>
              <TouchableOpacity 
                style={styles.retryButton}
                onPress={testServerConnection}
                disabled={isLoading}
              >
                <Text style={styles.retryButtonText}>🔄 Retry Connection</Text>
      </TouchableOpacity>
    </View>

            {/* Test Credentials Info */}
            <View style={styles.testInfo}>
              <Text style={styles.testInfoTitle}>Test Credentials:</Text>
              <Text style={styles.testInfoText}><EMAIL> / Admin123!</Text>
              <Text style={styles.testInfoText}><EMAIL> / Agent123!</Text>
            </View>

            {/* Footer */}
            <View style={styles.footer}>
              <Text style={styles.footerText}>
                Don't have an account?{' '}
                <Text style={styles.footerLink}>Contact Admin</Text>
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  content: {
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  brandSection: {
    alignItems: 'center',
    marginBottom: 48,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
  },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  logoText: {
    fontSize: 32,
    fontWeight: '700',
    color: colors.text.white,
  },
  brandTitle: {
    ...typography.h1,
    marginBottom: 4,
  },
  brandSubtitle: {
    ...typography.bodySmall,
    color: colors.text.secondary,
  },
  formSection: {
    marginBottom: 32,
  },
  formTitle: {
    ...typography.h2,
    marginBottom: 8,
    textAlign: 'center',
  },
  formSubtitle: {
    ...typography.bodySmall,
    textAlign: 'center',
    marginBottom: 32,
    color: colors.text.secondary,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    ...typography.bodySmall,
    fontWeight: '600',
    marginBottom: 8,
    color: colors.text.primary,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    ...typography.bodySmall,
    color: colors.primary,
    fontWeight: '600',
  },
  signInButton: {
    marginTop: 8,
  },
  serverInfo: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderLeftWidth: 4,
  },
  serverInfoTitle: {
    ...typography.bodySmall,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 8,
  },
  serverInfoText: {
    ...typography.bodySmall,
    color: colors.text.secondary,
    marginBottom: 4,
  },
  retryButton: {
    marginTop: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.primary + '20',
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  retryButtonText: {
    ...typography.bodySmall,
    color: colors.primary,
    fontWeight: '600',
  },
  testInfo: {
    backgroundColor: colors.background.tertiary,
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  testInfoTitle: {
    ...typography.bodySmall,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 8,
  },
  testInfoText: {
    ...typography.bodySmall,
    color: colors.text.secondary,
    marginBottom: 4,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    ...typography.bodySmall,
    color: colors.text.secondary,
  },
  footerLink: {
    color: colors.primary,
    fontWeight: '600',
  },
});