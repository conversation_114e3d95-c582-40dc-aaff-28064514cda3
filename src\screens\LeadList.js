import React, { useState, useMemo, useEffect } from 'react';
import { View, Text, FlatList, StyleSheet, SafeAreaView, StatusBar, ActivityIndicator, RefreshControl, Alert } from 'react-native';
import { Card, Header, Input, Badge, colors, typography } from '../components/UIComponents';
import { API_ENDPOINTS } from '../config/api';

export default function LeadList({ navigation }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [leads, setLeads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch leads from backend
  const fetchLeads = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(API_ENDPOINTS.LEADS);
      if (!response.ok) throw new Error('Failed to fetch leads');
      const data = await response.json();
      if (data.success) {
        setLeads(data.data);
      } else {
        setError(data.message || 'Failed to fetch leads');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchLeads();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    fetchLeads();
  };

  const filteredLeads = useMemo(() => {
    if (!searchQuery.trim()) return leads;
    return leads.filter(lead => 
      lead.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      lead.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      lead.phone.includes(searchQuery)
    );
  }, [searchQuery, leads]);

  const getStatusBadge = (status) => {
    switch (status) {
      case 'new':
        return <Badge text="New" variant="primary" />;
      case 'contacted':
        return <Badge text="Contacted" variant="warning" />;
      case 'qualified':
        return <Badge text="Qualified" variant="success" />;
      default:
        return <Badge text="Unknown" variant="secondary" />;
    }
  };

  const getPriorityBadge = (priority) => {
    switch (priority) {
      case 'high':
        return <Badge text="High" variant="danger" />;
      case 'medium':
        return <Badge text="Medium" variant="warning" />;
      case 'low':
        return <Badge text="Low" variant="secondary" />;
      default:
        return <Badge text="Unknown" variant="secondary" />;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const renderLeadCard = ({ item }) => (
    <Card
      style={styles.leadCard}
      onPress={() => navigation.navigate('LeadDetail', { leadId: item.id })}
    >
      <View style={styles.leadHeader}>
        <View style={styles.leadInfo}>
          <View style={styles.leadNameRow}>
            <Text style={styles.leadName}>{item.title}</Text>
            {getStatusBadge(item.status)}
          </View>
        </View>
        <View style={styles.leadActions}>
          {getPriorityBadge(item.priority)}
        </View>
      </View>
      
      <View style={styles.leadFooter}>
        <View style={styles.leadStats}>
          <Text style={styles.statLabel}>Source</Text>
          <Text style={styles.statValue}>{item.source}</Text>
        </View>
        <View style={styles.leadStats}>
          <Text style={styles.statLabel}>Captured</Text>
          <Text style={styles.statValue}>{formatDate(item.capturedAt)}</Text>
        </View>
        <View style={styles.leadStats}>
          <Text style={styles.statLabel}>Notes</Text>
          <Text style={styles.statValue} numberOfLines={1}>
            {item.notes}
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>📋</Text>
      <Text style={styles.emptyStateTitle}>No Leads Found</Text>
      <Text style={styles.emptyStateMessage}>
        {searchQuery ? 'Try adjusting your search terms' : 'Leads will appear here once captured'}
      </Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
        <View style={[styles.content, { justifyContent: 'center', alignItems: 'center' }]}> 
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={{ marginTop: 16, color: colors.text.secondary }}>Loading leads...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
        <View style={[styles.content, { justifyContent: 'center', alignItems: 'center' }]}> 
          <Text style={{ color: colors.danger, marginBottom: 16 }}>Error: {error}</Text>
          <Text style={{ color: colors.text.secondary, marginBottom: 16 }}>Could not load leads from server.</Text>
          <Text style={{ color: colors.primary, fontWeight: '600' }} onPress={fetchLeads}>Tap to retry</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      <Header
        title="Captured Leads"
        subtitle={`${filteredLeads.length} leads found`}
        rightComponent={
          <View style={styles.headerActions}>
            <Text style={styles.headerActionText}>Filter</Text>
          </View>
        }
      />
      <View style={styles.content}>
        <View style={styles.searchContainer}>
          <Input
            placeholder="Search leads by name, email, or phone..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
          />
        </View>
      <FlatList
          data={filteredLeads}
        keyExtractor={(item) => item.id}
          renderItem={renderLeadCard}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
      />
    </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  searchContainer: {
    marginVertical: 16,
  },
  searchInput: {
    backgroundColor: colors.background.primary,
  },
  listContainer: {
    paddingBottom: 20,
  },
  leadCard: {
    marginVertical: 4,
  },
  leadHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  leadInfo: {
    flex: 1,
  },
  leadNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  leadName: {
    ...typography.h3,
    flex: 1,
  },
  leadActions: {
    alignItems: 'flex-end',
  },
  leadFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  leadStats: {
    flex: 1,
    alignItems: 'flex-start',
  },
  statLabel: {
    ...typography.caption,
    color: colors.text.light,
    marginBottom: 2,
  },
  statValue: {
    ...typography.bodySmall,
    fontWeight: '500',
    color: colors.text.primary,
  },
  separator: {
    height: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateTitle: {
    ...typography.h3,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateMessage: {
    ...typography.bodySmall,
    color: colors.text.secondary,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  headerActions: {
    paddingHorizontal: 8,
  },
  headerActionText: {
    ...typography.bodySmall,
    color: colors.primary,
    fontWeight: '600',
  },
});