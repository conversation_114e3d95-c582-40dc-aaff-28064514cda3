// API Configuration for React Native
// For React Native development, you need to use your computer's IP address instead of localhost

// Development - Change this to your computer's IP address
const DEV_API_URL = 'http://************:5000/api'; // Replace with your IP

// Production - Change this to your production server URL
const PROD_API_URL = 'https://your-production-server.com/api';

// Use development URL for now
const API_BASE_URL = __DEV__ ? DEV_API_URL : PROD_API_URL;

// API endpoints
export const API_ENDPOINTS = {
  // Authentication
  SIGN_IN: `${API_BASE_URL}/auth/signin`,
  SIGN_UP: `${API_BASE_URL}/auth/register`,
  FORGOT_PASSWORD: `${API_BASE_URL}/auth/forgot-password`,
  PROFILE: `${API_BASE_URL}/auth/profile`,
  UPDATE_PROFILE: `${API_BASE_URL}/auth/profile`,
  CHANGE_PASSWORD: `${API_BASE_URL}/auth/change-password`,
  LOGOUT: `${API_BASE_URL}/auth/logout`,
  
  // Health check
  HEALTH: `${API_BASE_URL.replace('/api', '')}/health`,

  // Leads
  LEADS: `${API_BASE_URL}/leads`,
  
  // Calls endpoints
  INITIATE_CALL: `${API_BASE_URL}/calls/initiate`,
  CALL_STATUS: (callId) => `${API_BASE_URL}/calls/${callId}/status`,
  END_CALL: (callId) => `${API_BASE_URL}/calls/${callId}`,
  CALL_HISTORY: (leadId) => `${API_BASE_URL}/calls/lead/${leadId}`,
  
  // Sinch endpoints
  SINCH_TOKEN: `${API_BASE_URL}/sinch/token`,
};

// API configuration
export const API_CONFIG = {
  BASE_URL: API_BASE_URL,
  TIMEOUT: 10000, // 10 seconds
  HEADERS: {
    'Content-Type': 'application/json',
  },
};

// Helper function to get your computer's IP address
export const getLocalIPAddress = () => {
  // You can find your IP address by running 'ipconfig' on Windows or 'ifconfig' on Mac/Linux
  // Common local IP addresses: *************, *************, **********
  return '************'; // Replace with your actual IP
};

// Helper function to test API connection
export const testAPIConnection = async () => {
  try {
    const response = await fetch(API_ENDPOINTS.HEALTH);
    const data = await response.json();
    console.log('✅ API connection successful:', data);
    return true;
  } catch (error) {
    console.error('❌ API connection failed:', error.message);
    return false;
  }
};

export default API_CONFIG; 