# Sinch Native SDK Integration Guide

This guide will help you integrate Sinch Voice SDK for real app-to-phone calling in your React Native app.

## Prerequisites

- React Native project with iOS and Android targets
- Sinch account with Voice capabilities
- Your Sinch App Key and App Secret (already configured in backend)

## iOS Setup

### 1. Install Sinch iOS SDK

Add to your `ios/Podfile`:
```ruby
pod 'SinchRTC', '~> 5.18.6'
```

Run:
```bash
cd ios && pod install
```

### 2. Create iOS Native Module

Create `ios/RNSinchModule.swift`:

```swift
import Foundation
import SinchRTC
import React

@objc(RNSinchModule)
class RNSinchModule: RCTEventEmitter {
    
    private var sinchClient: SINClient?
    private var currentCall: SINCall?
    
    override init() {
        super.init()
    }
    
    @objc
    override static func requiresMainQueueSetup() -> Bool {
        return false
    }
    
    override func supportedEvents() -> [String]! {
        return [
            "onClientStarted",
            "onClientFailed",
            "onCallEstablished",
            "onCallEnded",
            "onCallFailed",
            "onCallProgressing",
            "onIncomingCall"
        ]
    }
    
    @objc
    func initialize(_ userId: String, token: String, resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            self.sinchClient = Sinch.client(withApplicationKey: "7bab0400-93ad-4afc-926f-8f1c9b2ff748", environmentHost: "clientapi.sinch.com", userId: userId)
            
            self.sinchClient?.delegate = self
            self.sinchClient?.call().delegate = self
            
            self.sinchClient?.setSupportCalling(true)
            self.sinchClient?.start()
            
            resolver(true)
        }
    }
    
    @objc
    func callPhoneNumber(_ phoneNumber: String, resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
        guard let client = sinchClient else {
            rejecter("error", "Client not initialized", nil)
            return
        }
        
        DispatchQueue.main.async {
            let call = client.call().callPhoneNumber(phoneNumber)
            self.currentCall = call
            resolver(call?.callId ?? "")
        }
    }
    
    @objc
    func hangup(_ resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            self.currentCall?.hangup()
            self.currentCall = nil
            resolver(true)
        }
    }
    
    @objc
    func setMuted(_ muted: Bool, resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            self.currentCall?.mute(muted)
            resolver(true)
        }
    }
    
    @objc
    func setSpeakerOn(_ speakerOn: Bool, resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            // iOS speaker control
            try? AVAudioSession.sharedInstance().setCategory(.playAndRecord, options: speakerOn ? .defaultToSpeaker : [])
            resolver(true)
        }
    }
}

// MARK: - SINClientDelegate
extension RNSinchModule: SINClientDelegate {
    func clientDidStart(_ client: SINClient!) {
        sendEvent(withName: "onClientStarted", body: nil)
    }
    
    func clientDidFail(_ client: SINClient!, error: Error!) {
        sendEvent(withName: "onClientFailed", body: ["error": error.localizedDescription])
    }
}

// MARK: - SINCallDelegate
extension RNSinchModule: SINCallDelegate {
    func callDidProgress(_ call: SINCall!) {
        sendEvent(withName: "onCallProgressing", body: ["callId": call.callId])
    }
    
    func callDidEstablish(_ call: SINCall!) {
        sendEvent(withName: "onCallEstablished", body: ["callId": call.callId])
    }
    
    func callDidEnd(_ call: SINCall!) {
        sendEvent(withName: "onCallEnded", body: ["callId": call.callId])
        currentCall = nil
    }
    
    func callDidFail(_ call: SINCall!, error: Error!) {
        sendEvent(withName: "onCallFailed", body: ["callId": call.callId, "error": error.localizedDescription])
        currentCall = nil
    }
}
```

### 3. Create Objective-C Bridge

Create `ios/RNSinchModule.m`:

```objc
#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>

@interface RCT_EXTERN_MODULE(RNSinchModule, RCTEventEmitter)

RCT_EXTERN_METHOD(initialize:(NSString *)userId
                  token:(NSString *)token
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(callPhoneNumber:(NSString *)phoneNumber
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(hangup:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(setMuted:(BOOL)muted
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(setSpeakerOn:(BOOL)speakerOn
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

@end
```

## Android Setup (Kotlin)

### 1. Add Sinch Android SDK

Add to your `android/app/build.gradle`:

```gradle
dependencies {
    implementation 'com.sinch.android:rtc:5.18.6'
}
```

### 2. Create Kotlin Native Module

Create `android/app/src/main/java/com/leadgenapp/RNSinchModule.kt`:

```kotlin
package com.leadgenapp

import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.sinch.android.rtc.Sinch
import com.sinch.android.rtc.SinchClient
import com.sinch.android.rtc.calling.Call
import com.sinch.android.rtc.calling.CallClient
import com.sinch.android.rtc.calling.CallClientListener
import com.sinch.android.rtc.calling.CallListener

class RNSinchModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    
    private var sinchClient: SinchClient? = null
    private var currentCall: Call? = null
    private val reactContext: ReactApplicationContext = reactContext

    override fun getName(): String {
        return "RNSinchModule"
    }

    @ReactMethod
    fun initialize(userId: String, token: String, promise: Promise) {
        try {
            sinchClient = Sinch.getSinchClientBuilder()
                .context(reactContext)
                .userId(userId)
                .applicationKey("7bab0400-93ad-4afc-926f-8f1c9b2ff748")
                .applicationSecret("MPe7MuwHLkyspCmcR9jnXA==")
                .environmentHost("clientapi.sinch.com")
                .build()

            sinchClient?.setSupportCalling(true)
            sinchClient?.startListeningOnActiveConnection()
            sinchClient?.start()

            sinchClient?.callClient?.addCallClientListener(object : CallClientListener {
                override fun onIncomingCall(callClient: CallClient, call: Call) {
                    currentCall = call
                    setupCallListener(call)
                    sendEvent("onIncomingCall", null)
                }
            })

            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("error", e.message)
        }
    }

    @ReactMethod
    fun callPhoneNumber(phoneNumber: String, promise: Promise) {
        try {
            if (sinchClient != null) {
                currentCall = sinchClient?.callClient?.callPhoneNumber(phoneNumber)
                currentCall?.let { call ->
                    setupCallListener(call)
                    promise.resolve(call.callId)
                } ?: promise.reject("error", "Failed to create call")
            } else {
                promise.reject("error", "Client not initialized")
            }
        } catch (e: Exception) {
            promise.reject("error", e.message)
        }
    }

    @ReactMethod
    fun hangup(promise: Promise) {
        try {
            currentCall?.hangup()
            currentCall = null
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("error", e.message)
        }
    }

    @ReactMethod
    fun setMuted(muted: Boolean, promise: Promise) {
        try {
            currentCall?.mute(muted)
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("error", e.message)
        }
    }

    @ReactMethod
    fun setSpeakerOn(speakerOn: Boolean, promise: Promise) {
        try {
            // Android speaker control implementation
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("error", e.message)
        }
    }

    private fun setupCallListener(call: Call) {
        call.addCallListener(object : CallListener {
            override fun onCallEnded(endedCall: Call) {
                sendEvent("onCallEnded", null)
                currentCall = null
            }

            override fun onCallEstablished(establishedCall: Call) {
                sendEvent("onCallEstablished", null)
            }

            override fun onCallProgressing(progressingCall: Call) {
                sendEvent("onCallProgressing", null)
            }

            override fun onShouldSendPushNotification(call: Call, pushPairs: List<com.sinch.android.rtc.calling.PushPair>) {
                sendEvent("onShouldSendPushNotification", null)
            }
        })
    }

    private fun sendEvent(eventName: String, params: WritableMap?) {
        reactContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit(eventName, params)
    }

    override fun onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy()
        sinchClient?.terminate()
        sinchClient = null
        currentCall = null
    }
}
```

### 3. Create Kotlin Package Class

Create `android/app/src/main/java/com/leadgenapp/RNSinchPackage.kt`:

```kotlin
package com.leadgenapp

import com.facebook.react.ReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.uimanager.ViewManager

class RNSinchPackage : ReactPackage {
    override fun createViewManagers(reactContext: ReactApplicationContext): List<ViewManager<*, *>> {
        return emptyList()
    }

    override fun createNativeModules(reactContext: ReactApplicationContext): List<NativeModule> {
        return listOf(RNSinchModule(reactContext))
    }
}
```

### 4. Register the Package

Add to `android/app/src/main/java/com/leadgenapp/MainApplication.kt`:

```kotlin
override fun getPackages(): List<ReactPackage> =
    PackageList(this).packages.apply {
      // Packages that cannot be autolinked yet can be added manually here
      add(RNSinchPackage())
    }
```

## Permissions

### iOS
Add to `ios/LeadGenApp/Info.plist`:
```xml
<key>NSMicrophoneUsageDescription</key>
<string>This app needs access to microphone for voice calls</string>
```

### Android
Add to `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.VIBRATE" />
```

## Testing

1. Build and run your app
2. Navigate to a lead detail screen
3. Tap "Call Lead"
4. The app should initialize Sinch and make a real call to the lead's phone number
5. Test mic mute and speaker controls

## Troubleshooting

- Ensure your Sinch App Key and Secret are correct
- Check that the native modules are properly linked
- Verify microphone permissions are granted
- Check network connectivity for Sinch services

## Next Steps

- Add call recording functionality
- Implement call history tracking
- Add push notifications for incoming calls
- Implement call quality monitoring 
- Implement call quality monitoring 