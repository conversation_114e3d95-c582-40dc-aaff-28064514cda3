/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, { useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import AuthStack from './src/navigation/AuthStack';
import MainStack from './src/navigation/MainStack';

import type { JSX } from 'react';

export default function App(): JSX.Element {
  const [signedIn, setSignedIn] = useState(false);

  return (
    <NavigationContainer>
      {signedIn ? (
        <MainStack />
      ) : (
        <AuthStack onSignIn={() => setSignedIn(true)} />
      )}
    </NavigationContainer>
  );
}
