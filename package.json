{"name": "LeadGenApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "setup-sinch-ios": "cd ios && pod install", "setup-sinch-android": "echo 'Android setup completed. Please follow NATIVE_SETUP.md for manual steps.'", "setup-sinch": "npm run setup-sinch-ios && npm run setup-sinch-android"}, "dependencies": {"@react-native/new-app-screen": "0.80.0", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.20", "react": "18.2.0", "react-native": "0.72.6", "react-native-safe-area-context": "^5.5.0", "react-native-screens": "^4.11.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@react-native/typescript-config": "0.80.0", "@types/jest": "^29.5.13", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}