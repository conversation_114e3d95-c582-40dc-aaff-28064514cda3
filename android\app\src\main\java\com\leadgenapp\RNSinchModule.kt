package com.leadgenapp

import android.util.Log
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.sinch.android.rtc.*
import com.sinch.android.rtc.calling.*

class RNSinchModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    private var sinchClient: SinchClient? = null
    private var currentCall: Call? = null
    private val reactContext: ReactApplicationContext = reactContext

    override fun getName(): String = "RNSinchModule"

    @ReactMethod
    fun initialize(userId: String, credentials: ReadableMap, promise: Promise) {
        try {
            Log.d("Sinch", "=== SINCH INITIALIZATION START ===")
            Log.d("Sinch", "UserId: $userId")
            Log.d("Sinch", "Credentials received: ${credentials.toString()}")
            
            if (sinchClient == null) {
                val token = credentials.getString("token") ?: throw Exception("Token is required")
                val appKey = credentials.getString("appKey") ?: throw Exception("App key is required")
                val environment = credentials.getString("environment") ?: "ocra.api.sinch.com"
                
                Log.d("Sinch", "Token length: ${token.length}")
                Log.d("Sinch", "AppKey: $appKey")
                Log.d("Sinch", "Environment: $environment")
                
                // Initialize SinchClient using the correct SDK 6.x API
                Log.d("Sinch", "Creating SinchClient...")
                sinchClient = SinchClient.builder()
                    .context(reactContext)
                    .applicationKey(appKey)
                    .environmentHost(environment)
                    .userId(userId)
                    .build()

                Log.d("Sinch", "SinchClient created successfully")

                sinchClient?.addSinchClientListener(object : SinchClientListener {
                    override fun onClientStarted(client: SinchClient) {
                        Log.d("Sinch", "✅ CLIENT STARTED SUCCESSFULLY")
                        sendEvent("onClientStarted", null)
                    }
                    
                    override fun onClientFailed(client: SinchClient, error: SinchError) {
                        Log.e("Sinch", "❌ CLIENT FAILED: ${error.message}")
                        Log.e("Sinch", "Error code: ${error.code}")
                        sendEvent("onClientFailed", Arguments.createMap().apply {
                            putString("error", error.message)
                            putInt("code", error.code)
                        })
                    }
                    
                    override fun onCredentialsRequired(clientRegistration: ClientRegistration) {
                        Log.d("Sinch", "🔑 CREDENTIALS REQUIRED - Registering with JWT token")
                        try {
                            clientRegistration.register(token)
                            Log.d("Sinch", "✅ Token registration attempted")
                        } catch (e: Exception) {
                            Log.e("Sinch", "❌ Token registration failed: ${e.message}")
                        }
                    }
                    
                    override fun onLogMessage(level: Int, area: String, message: String) {
                        Log.d("Sinch", "SDK[$area]: $message")
                    }
                    
                    override fun onUserRegistered() {
                        Log.d("Sinch", "✅ USER REGISTERED SUCCESSFULLY")
                    }
                    
                    override fun onUserRegistrationFailed(error: SinchError) {
                        Log.e("Sinch", "❌ USER REGISTRATION FAILED: ${error.message}")
                    }
                    
                    override fun onPushTokenRegistered() {
                        Log.d("Sinch", "✅ PUSH TOKEN REGISTERED")
                    }
                    
                    override fun onPushTokenRegistrationFailed(error: SinchError) {
                        Log.e("Sinch", "❌ PUSH TOKEN REGISTRATION FAILED: ${error.message}")
                    }
                    
                    override fun onPushTokenUnregistered() {
                        Log.d("Sinch", "Push token unregistered")
                    }
                    
                    override fun onPushTokenUnregistrationFailed(error: SinchError) {
                        Log.e("Sinch", "Push token unregistration failed: ${error.message}")
                    }
                })

                Log.d("Sinch", "Starting SinchClient...")
                // Start the client
                sinchClient?.start()
                Log.d("Sinch", "SinchClient.start() called")
            } else {
                Log.d("Sinch", "SinchClient already exists, checking if started...")
                Log.d("Sinch", "Is started: ${sinchClient?.isStarted}")
            }
            
            Log.d("Sinch", "=== SINCH INITIALIZATION END ===")
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e("Sinch", "❌ INITIALIZATION FAILED: ${e.message}")
            Log.e("Sinch", "Stack trace: ", e)
            promise.reject("error", e.message)
        }
    }

    @ReactMethod
    fun callPhoneNumber(phoneNumber: String, promise: Promise) {
        try {
            Log.d("Sinch", "=== MAKING CALL ===")
            val client = sinchClient
            Log.d("Sinch", "Client exists: ${client != null}")
            Log.d("Sinch", "Client is started: ${client?.isStarted}")
            
            if (client != null && client.isStarted) {
                val callController = client.callController
                Log.d("Sinch", "CallController obtained: ${callController != null}")
                
                // Make the call using callPhoneNumber with CLI (Calling Line Identifier)
                val cli = "+447418631901" // Your Sinch phone number
                Log.d("Sinch", "Attempting call to: $phoneNumber with CLI: $cli")
                
                val call = callController.callPhoneNumber(phoneNumber, cli)
                currentCall = call
                
                // Set up call listener
                setupCallListener(call)
                
                Log.d("Sinch", "✅ Call initiated successfully to: $phoneNumber with CLI: $cli")
                promise.resolve(call.callId ?: "unknown-call-id")
            } else {
                val errorMsg = when {
                    client == null -> "Sinch client is null"
                    !client.isStarted -> "Sinch client not started (isStarted = false)"
                    else -> "Unknown client state"
                }
                Log.e("Sinch", "❌ CALL FAILED: $errorMsg")
                promise.reject("error", "Sinch client not started: $errorMsg")
            }
        } catch (e: Exception) {
            Log.e("Sinch", "❌ CALL EXCEPTION: ${e.message}")
            Log.e("Sinch", "Stack trace: ", e)
            promise.reject("error", e.message)
        }
    }

    @ReactMethod
    fun hangup(promise: Promise) {
        try {
            currentCall?.hangup()
            currentCall = null
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("error", e.message)
        }
    }

    @ReactMethod
    fun setMuted(muted: Boolean, promise: Promise) {
        try {
            // For now, just resolve - we'll implement when we find the correct audio controller API
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("error", e.message)
        }
    }

    @ReactMethod
    fun setSpeakerOn(speakerOn: Boolean, promise: Promise) {
        try {
            // For now, just resolve - we'll implement when we find the correct audio controller API
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("error", e.message)
        }
    }

    @ReactMethod
    fun isClientReady(promise: Promise) {
        try {
            val client = sinchClient
            val isReady = client != null && client.isStarted
            
            Log.d("Sinch", "🔍 isClientReady check: client exists=${client != null}, isStarted=${client?.isStarted}, result=$isReady")
            
            promise.resolve(Arguments.createMap().apply {
                putBoolean("isReady", isReady)
                putBoolean("clientExists", client != null)
                putBoolean("clientStarted", client?.isStarted ?: false)
            })
        } catch (e: Exception) {
            Log.e("Sinch", "❌ Error checking client ready state: ${e.message}")
            promise.reject("error", e.message)
        }
    }

    @ReactMethod
    fun getClientState(promise: Promise) {
        try {
            val client = sinchClient
            
            promise.resolve(Arguments.createMap().apply {
                putBoolean("clientExists", client != null)
                putBoolean("isStarted", client?.isStarted ?: false)
                putString("clientState", when {
                    client == null -> "NULL"
                    client.isStarted -> "STARTED"
                    else -> "NOT_STARTED"
                })
            })
        } catch (e: Exception) {
            promise.reject("error", e.message)
        }
    }

    private fun setupCallListener(call: Call) {
        call.addCallListener(object : CallListener {
            override fun onCallProgressing(call: Call) {
                Log.d("Sinch", "Call progressing")
                sendEvent("onCallProgressing", null)
            }
            
            override fun onCallEstablished(call: Call) {
                Log.d("Sinch", "Call established")
                sendEvent("onCallEstablished", null)
            }
            
            override fun onCallEnded(call: Call) {
                Log.d("Sinch", "Call ended")
                sendEvent("onCallEnded", Arguments.createMap().apply {
                    putString("reason", "Call ended")
                })
                currentCall = null
            }
        })
    }

    private fun sendEvent(eventName: String, params: WritableMap?) {
        reactContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit(eventName, params)
    }

    override fun onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy()
        currentCall?.hangup()
        sinchClient?.terminateGracefully()
        sinchClient = null
        currentCall = null
    }
} 