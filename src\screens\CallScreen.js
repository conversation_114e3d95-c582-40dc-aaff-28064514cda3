import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, StatusBar, Animated, Alert } from 'react-native';
import { <PERSON><PERSON>, Button, Card, colors, typography } from '../components/UIComponents';
import { API_ENDPOINTS } from '../config/api';
import SinchService from '../services/SinchService';

export default function CallScreen({ route, navigation }) {
  const { leadId } = route.params;
  const [lead, setLead] = useState(null);
  const [callStatus, setCallStatus] = useState('idle'); // idle, initializing, connecting, connected, ended
  const [callDuration, setCallDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pulseAnim] = useState(new Animated.Value(1));
  const [sinchInitialized, setSinchInitialized] = useState(false);

  // Fetch lead data
  const fetchLead = async () => {
    try {
      const response = await fetch(`${API_ENDPOINTS.LEADS}/${leadId}`);
      if (!response.ok) throw new Error('Failed to fetch lead');
      const data = await response.json();
      if (data.success) {
        setLead(data.data);
      } else {
        setError(data.message || 'Failed to fetch lead');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Initialize Sinch client
  const initializeSinch = async () => {
    try {
      setCallStatus('initializing');
      
      // Get Sinch token from backend
      const response = await fetch(API_ENDPOINTS.SINCH_TOKEN, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          userId: `agent_${leadId}` // Use leadId as part of userId for uniqueness
        }),
      });

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Failed to get Sinch token');
      }

      // Initialize Sinch client with complete credentials
      await SinchService.initialize(`agent_${leadId}`, data.data);
      setSinchInitialized(true);
      setCallStatus('idle');
      
      console.log('✅ Sinch client ready for calling');
    } catch (error) {
      console.error('❌ Failed to initialize Sinch:', error);
      setError('Failed to initialize calling service: ' + error.message);
      setCallStatus('idle');
    }
  };

  useEffect(() => {
    fetchLead();
  }, [leadId]);

  useEffect(() => {
    if (lead && !sinchInitialized) {
      initializeSinch();
    }
  }, [lead, sinchInitialized]);

  useEffect(() => {
    let interval;
    if (callStatus === 'connected') {
      interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [callStatus]);

  useEffect(() => {
    if (callStatus === 'connecting') {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      pulseAnim.setValue(1);
    }
  }, [callStatus, pulseAnim]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      SinchService.cleanup();
    };
  }, []);

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartCall = async () => {
    if (!lead || !sinchInitialized) {
      Alert.alert('Error', 'Calling service not ready');
      return;
    }

    setCallStatus('connecting');
    try {
      console.log('📞 Preparing to make call...');
      
      // Wait for Sinch client to be ready
      console.log('⏳ Waiting for Sinch client to be ready...');
      await SinchService.waitForClientReady(10000); // Wait up to 10 seconds
      
      console.log('✅ Sinch client is ready, making call...');
      // Make real call using Sinch
      await SinchService.callPhoneNumber(lead.phone);
      console.log('📞 Real call initiated to:', lead.phone);
    } catch (error) {
      setCallStatus('idle');
      const errorMessage = error.message || 'Failed to initiate call';
      Alert.alert('Call Failed', `${errorMessage}\n\nPlease check:\n• Internet connection\n• App permissions\n• Sinch service status`);
      console.error('Call initiation error:', error);
    }
  };

  const handleEndCall = async () => {
    try {
      await SinchService.hangup();
      setCallStatus('ended');
      setTimeout(() => navigation.goBack(), 1000);
    } catch (error) {
      console.error('End call error:', error);
      setCallStatus('ended');
      setTimeout(() => navigation.goBack(), 1000);
    }
  };

  const handleMuteToggle = async () => {
    try {
      const newMutedState = !isMuted;
      await SinchService.setMuted(newMutedState);
      setIsMuted(newMutedState);
    } catch (error) {
      console.error('Mute toggle error:', error);
      Alert.alert('Error', 'Failed to toggle mute');
    }
  };

  const handleSpeakerToggle = async () => {
    try {
      const newSpeakerState = !isSpeakerOn;
      await SinchService.setSpeakerOn(newSpeakerState);
      setIsSpeakerOn(newSpeakerState);
    } catch (error) {
      console.error('Speaker toggle error:', error);
      Alert.alert('Error', 'Failed to toggle speaker');
    }
  };

  const getStatusText = () => {
    switch (callStatus) {
      case 'initializing':
        return 'Initializing...';
      case 'connecting':
        return 'Connecting...';
      case 'connected':
        return 'Connected';
      case 'ended':
        return 'Call Ended';
      default:
        return 'Ready to Call';
    }
  };

  const getStatusColor = () => {
    switch (callStatus) {
      case 'initializing':
        return colors.warning;
      case 'connecting':
        return colors.warning;
      case 'connected':
        return colors.accent;
      case 'ended':
        return colors.danger;
      default:
        return colors.text.secondary;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          title="Call Lead"
          leftComponent={
            <Button
              title="Back"
              variant="outline"
              size="small"
              onPress={() => navigation.goBack()}
              textStyle={{ color: colors.text.white }}
              style={{ borderColor: colors.text.white }}
            />
          }
        />
        <View style={styles.loadingContainer}>
          <Text style={{ color: colors.text.white }}>Loading lead...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !lead) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          title="Call Lead"
          leftComponent={
            <Button
              title="Back"
              variant="outline"
              size="small"
              onPress={() => navigation.goBack()}
              textStyle={{ color: colors.text.white }}
              style={{ borderColor: colors.text.white }}
            />
          }
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error || 'Lead not found'}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary} />
      
      <Header
        title="Call Lead"
        leftComponent={
          <Button
            title="Back"
            variant="outline"
            size="small"
            onPress={() => navigation.goBack()}
            textStyle={{ color: colors.text.white }}
            style={{ borderColor: colors.text.white }}
          />
        }
      />

      <View style={styles.content}>
        {/* Lead Info */}
        <Card style={styles.leadCard}>
          <View style={styles.leadInfo}>
            <View style={styles.leadAvatar}>
              <Text style={styles.leadAvatarText}>
                {lead.title.charAt(0).toUpperCase()}
              </Text>
            </View>
            <Text style={styles.leadName}>{lead.title}</Text>
            <Text style={styles.leadPhone}>{lead.phone}</Text>
          </View>
        </Card>

        {/* Call Status */}
        <View style={styles.statusSection}>
          <Animated.View 
            style={[
              styles.statusIndicator,
              { 
                backgroundColor: getStatusColor(),
                transform: [{ scale: pulseAnim }]
              }
            ]}
          />
          <Text style={[styles.statusText, { color: getStatusColor() }]}>
            {getStatusText()}
          </Text>
          {callStatus === 'connected' && (
            <Text style={styles.durationText}>{formatDuration(callDuration)}</Text>
          )}
        </View>

        {/* Call Controls */}
        <View style={styles.controlsSection}>
          {callStatus === 'idle' && sinchInitialized && (
            <Button
              title="📞 Start Call"
              onPress={handleStartCall}
              variant="primary"
              size="large"
              style={styles.startCallButton}
            />
          )}

          {callStatus === 'initializing' && (
            <View style={styles.connectingControls}>
              <Text style={styles.connectingText}>Initializing calling service...</Text>
            </View>
          )}

          {callStatus === 'connecting' && (
            <View style={styles.connectingControls}>
              <Text style={styles.connectingText}>Initiating call...</Text>
              <Button
                title="❌ Cancel"
                onPress={() => setCallStatus('idle')}
                variant="danger"
                size="medium"
              />
            </View>
          )}

          {callStatus === 'connected' && (
            <View style={styles.callControls}>
              <View style={styles.controlRow}>
                <Button
                  title={isMuted ? "🔇" : "🎤"}
                  onPress={handleMuteToggle}
                  variant={isMuted ? "danger" : "secondary"}
                  size="large"
                  style={styles.controlButton}
                />
                <Button
                  title={isSpeakerOn ? "🔊" : "📱"}
                  onPress={handleSpeakerToggle}
                  variant={isSpeakerOn ? "primary" : "secondary"}
                  size="large"
                  style={styles.controlButton}
                />
              </View>
              
              <Button
                title="❌ End Call"
                onPress={handleEndCall}
                variant="danger"
                size="large"
                style={styles.endCallButton}
              />
            </View>
          )}

          {callStatus === 'ended' && (
            <View style={styles.endedControls}>
              <Text style={styles.endedText}>Call ended</Text>
              <Text style={styles.callSummary}>
                Duration: {formatDuration(callDuration)}
              </Text>
            </View>
          )}
        </View>

        {/* Call Notes */}
        {callStatus === 'connected' && (
          <Card style={styles.notesCard}>
            <Text style={styles.notesTitle}>Call Notes</Text>
            <Text style={styles.notesText}>
              Take notes during the call to track important information and follow-up actions.
            </Text>
          </Card>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    ...typography.h3,
    color: colors.text.secondary,
  },
  leadCard: {
    marginBottom: 24,
  },
  leadInfo: {
    alignItems: 'center',
  },
  leadAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  leadAvatarText: {
    fontSize: 32,
    fontWeight: '700',
    color: colors.text.white,
  },
  leadName: {
    ...typography.h2,
    marginBottom: 4,
    textAlign: 'center',
  },
  leadPhone: {
    ...typography.body,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  statusSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  statusIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginBottom: 12,
  },
  statusText: {
    ...typography.h3,
    fontWeight: '600',
    marginBottom: 8,
  },
  durationText: {
    ...typography.body,
    color: colors.text.secondary,
  },
  controlsSection: {
    flex: 1,
    justifyContent: 'center',
  },
  startCallButton: {
    alignSelf: 'center',
  },
  connectingControls: {
    alignItems: 'center',
  },
  connectingText: {
    ...typography.body,
    color: colors.text.secondary,
    marginBottom: 16,
  },
  callControls: {
    alignItems: 'center',
  },
  controlRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 24,
    gap: 20,
  },
  controlButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  endCallButton: {
    alignSelf: 'center',
  },
  endedControls: {
    alignItems: 'center',
  },
  endedText: {
    ...typography.h3,
    color: colors.danger,
    marginBottom: 8,
  },
  callSummary: {
    ...typography.body,
    color: colors.text.secondary,
  },
  notesCard: {
    marginTop: 24,
  },
  notesTitle: {
    ...typography.h3,
    marginBottom: 8,
  },
  notesText: {
    ...typography.body,
    color: colors.text.secondary,
    lineHeight: 22,
  },
});